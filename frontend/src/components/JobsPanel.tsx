import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Icon<PERSON>utton,
  <PERSON>lapse,
  Badge,
  Divider,
  List,
  CircularProgress,
  <PERSON>,
  Button
} from '@mui/material';
import {
  Work,
  ExpandMore,
  ExpandLess,
  Notifications,
  NotificationsNone
} from '@mui/icons-material';
import JobItem from './JobItem';

export interface JobData {
  jobId: string;
  mediaItemId: string;
  filename?: string;
  mimeType?: string;
  baseUrl?: string;
  status: string;
  message: string;
  progress: number;
  quality: string;
  uploadToGooglePhotos: boolean;
  createdAt: string;
  completedAt?: string;
  compressionRatio?: number;
  error?: string;
  mediaType: 'Photo' | 'Video';
  googlePhotosUrl?: string;
  compressedGooglePhotosUrl?: string;
  creditsUsed?: number;
  previewImagePath?: string;
}

interface JobsPanelProps {
  // Panel state
  isCollapsed: boolean;
  onToggleCollapse: () => void;

  // Jobs data
  jobs: JobData[];
  onClearJob: (jobId: string) => void;
  onClearAllJobs: () => void;

  // Media items for preview images
  mediaItems: Array<{
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  }>;

  // Token for API calls
  token: string | null;
}

const JobsPanel: React.FC<JobsPanelProps> = ({
  isCollapsed,
  onToggleCollapse,
  jobs,
  onClearJob,
  onClearAllJobs,
  mediaItems,
  token
}) => {
  const [lastJobCount, setLastJobCount] = useState(0);
  
  // Count completed jobs for notification badge
  const completedJobs = jobs.filter(job => job.status.toLowerCase() === 'completed');
  const activeJobs = jobs.filter(job => !['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase()));
  const hasActiveJobs = activeJobs.length > 0;
  
  // Update last job count when panel is expanded
  useEffect(() => {
    if (!isCollapsed) {
      setLastJobCount(jobs.length);
    }
  }, [isCollapsed, jobs.length]);
  
  // Calculate notification count (new completed jobs since last view)
  const newCompletedCount = Math.max(0, completedJobs.length - lastJobCount);
  
  // Get media item for job preview
  const getMediaItemForJob = (job: JobData) => {
    return mediaItems.find(item => item.id === job.mediaItemId);
  };

  // Sort jobs by creation date (newest first)
  const sortedJobs = [...jobs].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  return (
    <Paper
      elevation={0}
      sx={{
        width: isCollapsed ? { xs: '100%', md: 60 } : { xs: '100%', md: 320 },
        height: 'fit-content',
        maxHeight: { xs: 'none', md: 'calc(100vh - 40px)' },
        position: { xs: 'static', md: 'sticky' },
        top: { md: 20 },
        alignSelf: 'flex-start',
        backgroundColor: 'grey.50',
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        transition: 'width 0.3s ease',
        overflow: 'hidden',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        flexShrink: 0,
        zIndex: 99
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: isCollapsed ? 1 : 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: isCollapsed ? { xs: 'center', md: 'center' } : 'space-between',
          backgroundColor: 'grey.50',
          minHeight: isCollapsed ? 48 : 'auto',
          position: 'relative'
        }}
      >
        {!isCollapsed && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Work sx={{ fontSize: 20, color: 'text.secondary' }} />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 500,
                fontSize: '1.1rem',
                color: 'text.primary'
              }}
            >
              Jobs
            </Typography>
            {hasActiveJobs && (
              <Chip
                label={`${activeJobs.length} active`}
                size="small"
                color="primary"
                sx={{
                  height: 20,
                  fontSize: '0.7rem',
                  '& .MuiChip-label': {
                    px: 1
                  }
                }}
              />
            )}
          </Box>
        )}
        
        {isCollapsed && (
          <IconButton
            onClick={onToggleCollapse}
            size="small"
            sx={{
              display: { xs: 'none', md: 'flex' },
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: 'action.hover'
              }
            }}
          >
            <Badge
              badgeContent={newCompletedCount > 0 ? newCompletedCount : (hasActiveJobs ? '!' : 0)}
              color={hasActiveJobs ? 'primary' : 'secondary'}
              sx={{
                '& .MuiBadge-badge': {
                  fontSize: '0.6rem',
                  height: 16,
                  minWidth: 16
                }
              }}
            >
              {newCompletedCount > 0 || hasActiveJobs ? (
                <Notifications sx={{ fontSize: 20, color: 'primary.main' }} />
              ) : (
                <NotificationsNone sx={{ fontSize: 20, color: 'text.secondary' }} />
              )}
            </Badge>
          </IconButton>
        )}
        
        {isCollapsed && (
          <Typography
            variant="subtitle2"
            sx={{
              display: { xs: 'block', md: 'none' },
              fontWeight: 500,
              fontSize: '0.875rem',
              color: 'text.secondary',
              textAlign: 'center'
            }}
          >
            Jobs {newCompletedCount > 0 && `(${newCompletedCount} new)`}
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <IconButton
            onClick={onToggleCollapse}
            size="small"
            sx={{
              color: 'text.secondary',
              display: isCollapsed ? { xs: 'flex', md: 'none' } : 'flex',
              '&:hover': {
                backgroundColor: 'action.hover'
              }
            }}
          >
            {isCollapsed ? <ExpandMore /> : <ExpandLess />}
          </IconButton>
        </Box>
      </Box>

      {/* Collapsed State - Mobile notification */}
      {isCollapsed && (
        <Box sx={{
          display: { xs: 'flex', md: 'none' },
          p: 1,
          justifyContent: 'center',
          alignItems: 'center',
          gap: 1
        }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={hasActiveJobs ? <CircularProgress size={12} /> : <Work />}
            onClick={onToggleCollapse}
            sx={{
              textTransform: 'none',
              fontSize: '0.75rem',
              borderColor: hasActiveJobs ? 'primary.main' : 'divider',
              color: hasActiveJobs ? 'primary.main' : 'text.secondary'
            }}
          >
            {hasActiveJobs ? `${activeJobs.length} Active` : `${jobs.length} Jobs`}
            {newCompletedCount > 0 && ` (${newCompletedCount} new)`}
          </Button>
        </Box>
      )}

      {/* Expanded Content */}
      <Collapse in={!isCollapsed} timeout={300}>
        <Box sx={{ maxHeight: 'calc(100vh - 140px)', overflow: 'auto' }}>
          {jobs.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Work sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                No compression jobs yet
              </Typography>
              <Typography variant="caption" color="text.disabled">
                Start compressing media to see jobs here
              </Typography>
            </Box>
          ) : (
            <>
              {/* Header with Clear All button */}
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2, pb: 1 }}>
                <Typography variant="subtitle2" sx={{ fontSize: '0.8rem', fontWeight: 600, color: 'text.secondary' }}>
                  All Jobs ({jobs.length})
                </Typography>
                <Button
                  onClick={onClearAllJobs}
                  size="small"
                  variant="outlined"
                  sx={{
                    fontSize: '0.65rem',
                    minWidth: 'auto',
                    px: 1,
                    py: 0.25,
                    height: 'auto'
                  }}
                >
                  Clear All
                </Button>
              </Box>

              {/* Deletion Policy Notice */}
              <Box sx={{ px: 2, pb: 1 }}>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: '0.5rem',
                    color: 'text.disabled',
                    fontStyle: 'italic',
                    display: 'block',
                    lineHeight: 1.3
                  }}
                >
                  Jobs are deleted after 24 hours
                </Typography>
              </Box>

              {/* Individual Job Items */}
              <List sx={{ p: 0 }}>
                {sortedJobs.map((job, index) => (
                  <React.Fragment key={job.jobId}>
                    <JobItem
                      job={job}
                      mediaItem={getMediaItemForJob(job)}
                      token={token}
                      compact={false}
                      onClearJob={onClearJob}
                    />
                    {index < sortedJobs.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default JobsPanel;
