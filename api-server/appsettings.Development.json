{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=vidcompressor;Username=vidcompressor;Password=password"}, "Google": {"ClientId": "546390650743-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com", "RedirectUri": "http://localhost:3000/auth/callback"}, "GoogleCloud": {"ProjectId": "tranquil-bison-465923-v9", "Region": "us-central1", "InputBucketName": "tranquil-bison-465923-v9-vidcompressor-input-dev", "OutputBucketName": "tranquil-bison-465923-v9-vidcompressor-output-dev", "TempBucketName": "tranquil-bison-465923-v9-vidcompressor-temp-dev", "Transcoder": {"Location": "us-central1"}, "CloudTasks": {"ProjectId": "tranquil-bison-465923-v9", "Location": "us-central1", "QueueName": "video-compression-jobs-dev", "HandlerUrl": "https://6d5b58c5ddf8.ngrok-free.app"}}, "Stripe": {"PublishableKey": ""}, "Frontend": {"BaseUrl": "http://localhost:3000"}, "Firestore": {"ProjectId": "tranquil-bison-465923-v9", "ServiceAccountKeyPath": "", "DatabaseId": "gallerytuner", "UseEmulator": false, "EmulatorHost": "localhost:8080"}}